<template>
  <div class="sentence-practice-container">
    <el-header class="header">
      <div class="header-content">
        <el-button
          @click="goBack"
          icon="el-icon-arrow-left"
          size="large"
          class="back-button"
          circle
        ></el-button>
        <div class="header-info">
          <h1>句子练习</h1>
          <el-breadcrumb separator=">" class="breadcrumb">
            <el-breadcrumb-item @click="goToHome">
              <i class="el-icon-house"></i>
              <span>首页</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item @click="goToCourseList">
              <i class="el-icon-school"></i>
              <span>{{ getLevelName() }}</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item @click="goToCourseDetail">
              <i class="el-icon-notebook-1"></i>
              <span>{{ getCourseName() }}</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item>
              <i class="el-icon-edit-outline"></i>
              <span>句子练习</span>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
    </el-header>
    
    <el-main class="main-content">
      <div v-if="loading">
        <el-loading-spinner type="spinner"></el-loading-spinner>
        <span>加载中...</span>
      </div>
      
      <div v-else-if="sentence">
        <div class="practice-card">
          <!-- 进度条 -->
          <div class="progress-container">
            <el-progress 
              :percentage="progressPercentage" 
              :status="progressStatus"
              :stroke-width="8"
              text-inside
            />
          </div>
          
          <!-- 句子卡片 -->
          <el-card class="sentence-display-card">
            <div class="sentence-avatar">
              <div v-if="currentAvatarIndex === 0" class="avatar-icon">
                <i class="el-icon-user"></i>
              </div>
              <div v-else-if="currentAvatarIndex === 1" class="avatar-icon">
                <i class="el-icon-girl"></i>
              </div>
              <div v-else class="avatar-icon">
                <i class="el-icon-boy"></i>
              </div>
            </div>
            
            <div class="sentence-display">
              <div class="english-sentence">
                {{ sentence.english }}
              </div>
              
              <div class="sentence-audio">
                <el-button 
                  @click="playAudio" 
                  type="primary" 
                  icon="el-icon-video-play" 
                  circle
                ></el-button>
                <span>播放发音</span>
              </div>
            </div>
          </el-card>
          
          <!-- 语音设置控件 -->
          <div class="voice-settings">
            <div class="speed-control">
              <span>语速: {{ speedRate.toFixed(1) }}x</span>
              <el-slider 
                v-model="speedRate" 
                :min="0.5" 
                :max="2.0" 
                :step="0.1"
                style="width: 200px; margin-left: 10px;"
              />
            </div>
            
            <div class="voice-type-control">
              <span>发音类型:</span>
              <el-radio-group v-model="voiceType" style="margin-left: 10px;">
                <el-radio label="female">女声</el-radio>
                <el-radio label="male">男声</el-radio>
              </el-radio-group>
            </div>
          </div>
          
          <!-- 翻译输入区 -->
          <div class="translation-input-area">
            <h3>请用中文写出这句话：</h3>
            <el-input
              v-model="userTranslation"
              type="textarea"
              :rows="4"
              placeholder="请输入中文翻译..."
              clearable
            />
            
            <div class="options-container">
              <div v-if="showOptions" class="word-options">
                <el-button
                  v-for="option in translationOptions"
                  :key="option"
                  size="small"
                  @click="selectOption(option)"
                  class="option-button"
                >
                  {{ option }}
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button @click="showHint" type="warning" :disabled="hintUsed">
              {{ hintUsed ? '已使用提示' : '提示' }}
            </el-button>
            <el-button @click="checkAnswer" type="primary" :disabled="answerChecked">
              检查答案
            </el-button>
            <el-button 
              v-if="answerChecked" 
              @click="nextSentence" 
              type="success"
              :disabled="isLastSentence"
            >
              {{ isLastSentence ? '完成' : '下一句' }}
            </el-button>
          </div>
          
          <!-- 答案反馈 -->
          <div v-if="answerChecked" class="answer-feedback">
            <div v-if="isCorrect" class="correct-answer">
              <el-tag type="success">回答正确！</el-tag>
              <p>你的翻译：{{ userTranslation }}</p>
              <p>参考翻译：{{ sentence.chinese }}</p>
            </div>
            <div v-else class="incorrect-answer">
              <el-tag type="danger">回答有误</el-tag>
              <p>你的翻译：{{ userTranslation }}</p>
              <p>正确翻译：{{ sentence.chinese }}</p>
            </div>
          </div>
          
          <!-- 提示显示 -->
          <div v-if="hintUsed" class="hint-display">
            <el-tag type="info">提示</el-tag>
            <p>{{ hintText }}</p>
          </div>
        </div>
      </div>
      
      <div v-else class="error-state">
        <el-empty description="无法加载句子数据"></el-empty>
        <el-button @click="goBack" type="primary" style="margin-top: 20px;">
          返回课程详情
        </el-button>
      </div>
    </el-main>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getSentenceDetail } from '../services/api';
import type { Sentence } from '../services/api';

const router = useRouter();
const route = useRoute();
const sentence = ref<Sentence | null>(null);
const loading = ref(false);
const userTranslation = ref('');
const answerChecked = ref(false);
const isCorrect = ref(false);
const hintUsed = ref(false);
const hintText = ref('');
const showOptions = ref(true);
const currentAvatarIndex = ref(0);
const progressPercentage = ref(0);
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('');
const isLastSentence = ref(false);
// 新增：语速和语音类型设置
const speedRate = ref(1.0); // 默认正常速度
const voiceType = ref<'male' | 'female'>('female'); // 默认女声
const availableVoices = ref<SpeechSynthesisVoice[]>([]);

const sentenceId = route.params.sentenceId as string;

// 面包屑导航相关数据
const courseInfo = ref<{ level: string; courseId: string; courseName: string } | null>(null);

// 模拟翻译选项
const translationOptions = ref<string[]>([
  '我', '是', '一名', '中文', '老师', '的', '学生', '喜欢', '学习', '英语'
]);

const loadSentence = async () => {
  loading.value = true;
  try {
    const data = await getSentenceDetail(sentenceId);
    sentence.value = data;
    if (data) {
      // 根据句子内容生成一些相关的选项
      generateOptions(data.chinese);
      // 随机选择头像
      currentAvatarIndex.value = Math.floor(Math.random() * 3);

      // 解析句子ID获取课程信息
      const idParts = data.id.split('-');
      if (idParts.length >= 3) {
        const level = data.level;
        const courseId = idParts[1] || '';
        courseInfo.value = {
          level,
          courseId,
          courseName: getCourseNameById(level, courseId)
        };
      }
    }

    // 初始化语音列表
    initVoices();
  } catch (error) {
    console.error('Failed to load sentence:', error);
  } finally {
    loading.value = false;
  }
};

// 新增：初始化语音列表
const initVoices = () => {
  // 确保语音列表已加载
  const onVoicesChanged = () => {
    availableVoices.value = speechSynthesis.getVoices();
  };
  
  speechSynthesis.onvoiceschanged = onVoicesChanged;
  // 立即尝试获取一次，因为某些浏览器可能已经加载了语音
  onVoicesChanged();
};

// 新增：根据选择的语音类型获取对应的语音
const getVoiceByType = (type: 'male' | 'female') => {
  // 根据语音名称或语言判断性别
  const voices = availableVoices.value.filter(voice => 
    voice.lang.startsWith('en-') && voice.default
  );
  
  if (voices.length === 0) {
    return null;
  }
  
  // 简单的语音选择逻辑
  // 实际项目中可以根据语音名称更精确地判断性别
  const maleVoices = voices.filter(voice => 
    voice.name.toLowerCase().includes('male') || 
    voice.name.toLowerCase().includes('man') ||
    voice.name.toLowerCase().includes('microsoft david')
  );
  
  const femaleVoices = voices.filter(voice => 
    voice.name.toLowerCase().includes('female') || 
    voice.name.toLowerCase().includes('woman') || 
    voice.name.toLowerCase().includes('microsoft zira')
  );
  
  if (type === 'male' && maleVoices.length > 0) {
    return maleVoices[0];
  } else if (type === 'female' && femaleVoices.length > 0) {
    return femaleVoices[0];
  }
  
  // 如果没有找到匹配的性别语音，返回第一个可用的英语语音
  return voices[0];
};

const generateOptions = (chineseSentence: string) => {
  // 从中文句子中提取一些关键词作为选项
  const words = chineseSentence.split('');
  const uniqueWords = [...new Set(words.filter(w => w.trim() !== ''))];
  
  // 确保选项数量合适
  const tempOptions = [...uniqueWords];
  const additionalOptions = ['的', '是', '我', '你', '他', '她', '老师', '学生', '学习', '喜欢'];
  
  while (tempOptions.length < 8) {
    const randomIndex = Math.floor(Math.random() * additionalOptions.length);
    const randomOption = additionalOptions[randomIndex] || '';
    if (randomOption && !tempOptions.includes(randomOption)) {
      tempOptions.push(randomOption);
    }
  }
  
  // 打乱顺序
  translationOptions.value = tempOptions.sort(() => Math.random() - 0.5);
};

const goBack = () => {
  router.back();
};

// 面包屑导航函数
const goToHome = () => {
  router.push('/');
};

const goToCourseList = () => {
  if (courseInfo.value) {
    router.push(`/courses/${courseInfo.value.level}`);
  }
};

const goToCourseDetail = () => {
  if (courseInfo.value) {
    router.push(`/courses/${courseInfo.value.level}/${courseInfo.value.courseId}`);
  }
};

// 获取层级名称
const getLevelName = () => {
  if (!courseInfo.value) return '';

  const levelNames: { [key: string]: string } = {
    'primary-1': '小学一年级',
    'primary-2': '小学二年级',
    'primary-3': '小学三年级',
    'primary-4': '小学四年级',
    'primary-5': '小学五年级',
    'primary-6': '小学六年级',
    'junior-1': '初中一年级',
    'junior-2': '初中二年级',
    'junior-3': '初中三年级',
    'senior-1': '高中一年级',
    'senior-2': '高中二年级',
    'senior-3': '高中三年级'
  };

  return levelNames[courseInfo.value.level] || courseInfo.value.level;
};

// 获取课程名称
const getCourseName = () => {
  return courseInfo.value?.courseName || '';
};

// 根据课程ID获取课程名称
const getCourseNameById = (level: string, courseId: string): string => {
  const courseNames: { [key: string]: { [key: string]: string } } = {
    'primary-1': {
      '1': '基础日常用语',
      '2': '数字和颜色'
    },
    'primary-2': {
      '3': '学校生活',
      '4': '家庭和朋友'
    },
    'junior-1': {
      '5': '日常生活对话'
    },
    'senior-1': {
      '6': '高中英语基础'
    }
  };

  return courseNames[level]?.[courseId] || '未知课程';
};

const playAudio = () => {
  // 这里可以集成TTS服务来播放句子发音
  console.log('Playing audio for:', sentence.value?.english, 'Speed:', speedRate.value, 'Voice:', voiceType.value);
  // 实际项目中可以使用Web Speech API或其他TTS服务
  const utterance = new SpeechSynthesisUtterance(sentence.value?.english || '');
  utterance.lang = 'en-US';
  
  // 应用语速设置
  utterance.rate = speedRate.value;
  
  // 应用语音类型设置
  const selectedVoice = getVoiceByType(voiceType.value);
  if (selectedVoice) {
    utterance.voice = selectedVoice;
  }
  
  speechSynthesis.speak(utterance);
};

const selectOption = (option: string) => {
  userTranslation.value += option;
};

const showHint = () => {
  if (sentence.value) {
    hintUsed.value = true;
    // 提供首字作为提示
    hintText.value = `这句话的第一个字是："${sentence.value.chinese.charAt(0)}"`;
  }
};

const checkAnswer = () => {
  if (!sentence.value || !userTranslation.value.trim()) {
    return;
  }
  
  answerChecked.value = true;
  // 简单的匹配逻辑，实际应用中可以更复杂
  isCorrect.value = sentence.value.chinese === userTranslation.value.trim();
  
  // 更新进度状态
  progressStatus.value = isCorrect.value ? 'success' : 'exception';
  progressPercentage.value = isCorrect.value ? 100 : 0;
};

const nextSentence = () => {
  // 重置状态
  userTranslation.value = '';
  answerChecked.value = false;
  isCorrect.value = false;
  hintUsed.value = false;
  hintText.value = '';
  progressPercentage.value = 0;
  progressStatus.value = '';
  
  // 实际应用中这里应该导航到下一个句子
  // 由于是模拟环境，我们简单地返回课程详情页
  router.back();
};

onMounted(() => {
  loadSentence();
});
</script>

<style scoped>
.sentence-practice-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
  padding: 20px 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
  min-height: 80px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 60px;
}

.header h1 {
  margin: 0 0 8px 0;
  font-size: 26px;
  font-weight: 600;
  line-height: 1.3;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.back-button {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  width: 48px;
  height: 48px;
  font-size: 18px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.breadcrumb :deep(.el-breadcrumb__item) {
  color: rgba(255, 255, 255, 0.85);
  font-weight: 400;
  transition: all 0.3s ease;
}

.breadcrumb :deep(.el-breadcrumb__item:last-child) {
  color: rgba(255, 255, 255, 1);
  font-weight: 600;
}

.breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  min-height: 28px;
  background-color: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner i) {
  font-size: 13px;
  opacity: 0.9;
}

.breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner span) {
  font-size: inherit;
  font-weight: inherit;
}

.breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner:hover) {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner:hover i) {
  opacity: 1;
}

.breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  cursor: default;
  background-color: rgba(255, 255, 255, 0.25);
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover) {
  background-color: rgba(255, 255, 255, 0.25);
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner i) {
  opacity: 1;
  color: rgba(255, 255, 255, 1);
}

.breadcrumb :deep(.el-breadcrumb__separator) {
  color: rgba(255, 255, 255, 0.6);
  margin: 0 8px;
  font-weight: 400;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 16px 20px;
    min-height: 70px;
  }

  .header-content {
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }

  .header-info {
    width: 100%;
    min-height: 50px;
  }

  .header h1 {
    font-size: 22px;
    margin-bottom: 6px;
  }

  .breadcrumb {
    font-size: 13px;
  }

  .breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner) {
    padding: 4px 8px;
    min-height: 24px;
    gap: 4px;
  }

  .breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner i) {
    font-size: 12px;
  }

  .breadcrumb :deep(.el-breadcrumb__separator) {
    margin: 0 6px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 12px 16px;
    min-height: 60px;
  }

  .header h1 {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .breadcrumb {
    font-size: 12px;
  }

  .breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner) {
    padding: 3px 6px;
    min-height: 20px;
    gap: 3px;
  }

  .breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner span) {
    display: none;
  }

  .breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner i) {
    font-size: 14px;
  }

  .breadcrumb :deep(.el-breadcrumb__separator) {
    margin: 0 4px;
  }
}

.main-content {
  padding: 0 20px 20px;
  flex: 1;
}

.practice-card {
  max-width: 800px;
  margin: 0 auto;
}

.progress-container {
  margin-bottom: 30px;
}

.sentence-display-card {
  margin-bottom: 30px;
  padding: 30px;
  text-align: center;
  position: relative;
}

.sentence-avatar {
  position: absolute;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
}

.avatar-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
}

.sentence-display {
  margin-left: 80px;
}

.english-sentence {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.sentence-audio {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #606266;
}

.translation-input-area {
  margin-bottom: 20px;
}

.translation-input-area h3 {
  margin-bottom: 15px;
  color: #303133;
}

.options-container {
  margin-top: 15px;
}

.word-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.option-button {
  background-color: #ecf5ff;
  border-color: #d9ecff;
  color: #409eff;
}

.option-button:hover {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
}

.answer-feedback {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.correct-answer {
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
}

.incorrect-answer {
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
}

.answer-feedback p {
  margin: 10px 0 0;
  color: #606266;
}

.hint-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #ecf5ff;
  border-radius: 8px;
  text-align: center;
}

.hint-display p {
  margin: 10px 0 0;
  color: #409eff;
}

.loading, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

/* 新增：语音设置样式 */
.voice-settings {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.speed-control {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #303133;
  font-size: 14px;
}

.voice-type-control {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #303133;
  font-size: 14px;
}

/* 确保滑块在不同浏览器中显示一致 */
.el-slider__runway {
  background-color: #e4e7ed;
}

.el-slider__bar {
  background-color: #409eff;
}

.el-slider__button {
  border-color: #409eff;
}

.el-slider__button:hover {
  border-color: #66b1ff;
}

.el-slider__button.active {
  border-color: #409eff;
  box-shadow: 0 0 0 5px rgba(64, 158, 255, 0.1);
}
</style>