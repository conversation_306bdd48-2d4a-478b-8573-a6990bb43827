<template>
  <div class="course-detail-container">
    <el-header class="header">
      <div class="header-content">
        <el-button @click="goBack" icon="el-icon-arrow-left" size="large">-</el-button>
        <h1>{{ course?.title || '课程详情' }}</h1>
      </div>
    </el-header>
    
    <el-main class="main-content">
      <div v-if="loading">
        <el-loading-spinner type="spinner"></el-loading-spinner>
        <span>加载中...</span>
      </div>
      
      <div v-else-if="course">
        <el-card class="course-info-card">
          <div class="course-description">
            {{ course.description }}
          </div>
          <div class="course-stats">
            <span><i class="el-icon-document"></i> {{ course.sentences.length }} 个句子</span>
          </div>
        </el-card>
        
        <h2 class="sentences-title">句子列表</h2>
        <div class="sentences-list">
          <el-card 
            v-for="sentence in course.sentences" 
            :key="sentence.id" 
            class="sentence-card"
            shadow="hover"
            @click="goToSentencePractice(sentence.id)"
          >
            <div class="sentence-content">
              <div class="english-sentence">
                {{ sentence.english }}
              </div>
              <div class="chinese-sentence">
                {{ sentence.chinese }}
              </div>
            </div>
            <div class="card-footer">
              <el-button type="primary" size="small" class="practice-button">
                开始练习
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
      
      <div v-else class="error-state">
        <el-empty description="无法加载课程数据"></el-empty>
        <el-button @click="goBack" type="primary" style="margin-top: 20px;">
          返回课程列表
        </el-button>
      </div>
    </el-main>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getCourseDetail } from '../services/api';
import type { Course } from '../services/api';

const router = useRouter();
const route = useRoute();
const course = ref<Course | null>(null);
const loading = ref(false);

const level = route.params.level as string;
const id = route.params.id as string;

const loadCourseDetail = async () => {
  loading.value = true;
  try {
    const data = await getCourseDetail(level, id);
    course.value = data;
  } catch (error) {
    console.error('Failed to load course detail:', error);
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push('/');
};

const goToSentencePractice = (sentenceId: string) => {
  router.push({
    name: 'SentencePractice',
    params: { sentenceId }
  });
};

onMounted(() => {
  loadCourseDetail();
});
</script>

<style scoped>
.course-detail-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #409eff;
  color: white;
  padding: 15px 20px;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header h1 {
  margin: 0;
  font-size: 24px;
}

.main-content {
  padding: 0 20px 20px;
  flex: 1;
}

.course-info-card {
  margin-bottom: 30px;
}

.course-description {
  font-size: 16px;
  color: #303133;
  margin-bottom: 15px;
}

.course-stats {
  display: flex;
  gap: 20px;
  color: #606266;
}

.sentences-title {
  margin-bottom: 20px;
  font-size: 20px;
  color: #303133;
}

.sentences-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.sentence-card {
  cursor: pointer;
  transition: transform 0.3s;
}

.sentence-card:hover {
  transform: translateY(-5px);
}

.sentence-content {
  margin-bottom: 15px;
}

.english-sentence {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.chinese-sentence {
  font-size: 16px;
  color: #606266;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
}

.practice-button {
  background-color: #67c23a;
  border-color: #67c23a;
}

.loading, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}
</style>